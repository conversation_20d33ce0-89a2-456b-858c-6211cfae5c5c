# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

DrawnUI for .NET MAUI is a cross-platform rendering engine that draws UI using SkiaSharp instead of native controls. It supports iOS, MacCatalyst, Android, and Windows platforms.

**Key Technology Stack:**
- .NET 9 (minimum requirement)
- MAUI Controls 9.0.30+
- SkiaSharp v3 
- Hardware-accelerated rendering via Skia canvas

## Project Structure

**Core Projects:**
- `src/Maui/DrawnUi/` - Main DrawnUI library (AppoMobi.Maui.DrawnUi package)
- `src/Shared/` - Shared project containing cross-platform code
- `src/Maui/Addons/` - Additional functionality packages (Camera, Game, MapsUi, etc.)
- `src/Maui/Samples/Sandbox/` - Main demo/testing application
- `src/Tests/` - Unit tests and benchmarks

**Key Architectural Components:**
- `SkiaControl` - Base class for all drawn controls
- `Canvas` - MAUI view wrapper that hosts drawn controls
- `SkiaShell` - Navigation system for drawn apps
- Caching system with multiple strategies (Operations, Image, GPU, etc.)
- Gesture handling system for touch interactions
- Layout system supporting Grid, Stack, Absolute positioning
- Effects and animation system

## Build Commands

**Build the main library:**
```bash
dotnet build src/Maui/DrawnUi/DrawnUi.Maui.csproj
```

**Build solution:**
```bash
dotnet build src/DrawnUi.Maui.sln
```

**Run Sandbox demo:**
```bash
dotnet build src/Maui/Samples/Sandbox/Sandbox.csproj --configuration Debug
# Then run from Visual Studio or with platform-specific commands
```

**Create NuGet packages:**
```bash
cd nugets
./makenugets.bat  # Windows
```

**Run tests:**
```bash
dotnet test src/Tests/UnitTests/UnitTests.csproj
```

**Clean build artifacts:**
```powershell
# From src/ directory
./DeleteBinObj.ps1
```

## Development Setup

**Initialize DrawnUI in MauiProgram.cs:**
```csharp
builder.UseDrawnUi(new()
{
    UseDesktopKeyboard = true,
    DesktopWindow = new()
    {
        Width = 500,
        Height = 700
    }
});
```

**Platform Requirements:**
- Windows: Package as MSIX for hardware acceleration (`<WindowsPackageType>MSIX</WindowsPackageType>`)
- Minimum OS versions defined in project files
- Resources must be in `Resources/Raw` folder with subfolders allowed

## Important Development Notes

**SkiaSharp Version Control:**
- Default branch targets NET 9 with SkiaSharp v3
- NET 8 legacy support is disabled no longer supported, use versions 1.2.x (no longer updated)

**Caching Strategy:**
- `Operations` - For shapes, SVG, text (SKPicture-based)
- `Image` - Simple bitmap cache, works for large sizes
- `ImageDoubleBuffered` - Best for animations, double memory usage
- `GPU` - Hardware-accelerated, for graphics memory caching
- Never cache layers containing SkiaScroll, SkiaDrawer, SkiaCarousel and similar, SkiaMauiElement and derived controls (SkiaMauiEntry etc)

**Layout Differences from Standard MAUI:**
- Default `HorizontalOptions` and `VerticalOptions` are `Start`, not `Fill`
- Grid default spacing is 1, not 8
- Column/Row layouts require explicit Fill options for parent containers

**Code-behind UI creation
- Read Fluent.md file for info

**Resource Loading:**
- Web URLs: loaded from web
- `file://` prefix: loaded from native file system  
- Otherwise: loaded from `Resources\Raw` bundle folder

## Testing and Quality

**Test Projects:**
- `src/Tests/UnitTests/` - Unit tests for core functionality
- `src/Tests/SomeBenchmarks/` - Performance benchmarks

**Key Test Areas:**
- Canvas rendering (`CanvasTests.cs`)
- Layout system (`SkiaLayoutTests.cs`) 
- Image management (`SkiaImageManagerTests.cs`)
- Rendering pipeline (`RenderingTests.cs`)

## Addon Projects

**Available Addons:**
- `DrawnUi.Maui.Camera` - Camera integration with SkiaSharp
- `DrawnUi.Maui.Game` - Game development utilities  
- `DrawnUi.Maui.MapsUi` - Map controls
- `DrawnUi.Maui.Rive` - Rive animation support (Windows only)
- `DrawnUi.MauiGraphics` - Microsoft.Maui.Graphics integration

Each addon is a separate NuGet package that can be referenced independently.

## Core strategy
When creating code always consider that we we are inside a rendering engine trying to avoid new allocations during frame processing to avoid GC and looking for max fps! 
Apply this concept to new code and if you find existing code that can be adjusted to this line correct it at all times.