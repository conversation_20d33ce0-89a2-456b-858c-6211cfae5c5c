﻿<Project Sdk="Microsoft.NET.Sdk">

    <Target Name="IssueCustomWarning" BeforeTargets="BeforeBuild">
        <Warning Text="------ Building $(TargetFramework) using MAUI $(MauiVersion) ------" />
    </Target>

    <PropertyGroup>

        <WindowsEnableDebugging>true</WindowsEnableDebugging>
        <SkipValidateMauiImplicitPackageReferences>true</SkipValidateMauiImplicitPackageReferences>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <!--<DocumentationMarkdown>$(MSBuildProjectDirectory)\Generated.md</DocumentationMarkdown>-->
    </PropertyGroup>

    <ItemGroup Condition=" '$(Configuration)'=='Release' ">
        <PackageReference Include="Microsoft.SourceLink.GitHub" Version="8.0.0" PrivateAssets="All" />
    </ItemGroup>

    <PropertyGroup Condition=" '$(Configuration)'=='Release' ">
        <AllowedOutputExtensionsInPackageBuildOutputFolder>$(AllowedOutputExtensionsInPackageBuildOutputFolder);.pdb</AllowedOutputExtensionsInPackageBuildOutputFolder>
    </PropertyGroup>

    <PropertyGroup Condition="$(TargetFramework.Contains('windows')) == true Or $(TargetFramework.Contains('droid')) == true Or $(TargetFramework.Contains('ios')) == true Or $(TargetFramework.Contains('catalyst')) == true">
        <DefineConstants>$(DefineConstants);ONPLATFORM</DefineConstants>
    </PropertyGroup>

    <PropertyGroup>
        <Title>DrawnUI for .NET MAUI</Title>
        <PackageId>AppoMobi.Maui.DrawnUi</PackageId>
        <Description>Cross-platform rendering engine for .NET MAUI to draw your UI with SkiaSharp</Description>
        <PackageTags>maui drawnui skia skiasharp draw ui</PackageTags>
        <PackageIcon>icon128.png</PackageIcon>
        <CreatePackage>false</CreatePackage>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
        <NoWarn>1701;1702;XLS0505</NoWarn>
        <WarningsAsErrors>$(WarningsAsErrors);CS0108;XLS0501</WarningsAsErrors>
    </PropertyGroup>

    <ItemGroup>
        <Compile Remove="Draw\SkiaControl.Legacy.cs" />
        <Compile Remove="Draw\SkiaControl.States.cs" />
        <Compile Remove="Draw\SkiaControlVisual.cs" />
        <Compile Remove="Draw\SkiaGif.cs" />
        <Compile Remove="Internals\ConditionalStyle.cs" />
        <Compile Remove="Internals\Models\ConditionalStyle.cs" />
        <Compile Remove="Platforms\Windows\ViewChainPanel.Windows.cs" />
        <Compile Remove="Platforms\Windows\Views\AngleSwapChainPanel.cs" />
        <Compile Remove="Controls\Extensions\**" />
        <Compile Remove="Platforms\Windows\Controls\**" />
    </ItemGroup>


    <ItemGroup>
        <None Include="..\..\..\icon128.png">
            <Pack>True</Pack>
            <PackagePath>\</PackagePath>
        </None>
        <None Include="..\..\..\README.md" Link="README.md">
            <PackagePath>\</PackagePath>
            <Pack>True</Pack>
        </None>
    </ItemGroup>

    <ItemGroup>
        <!--parsing markdown text-->
        <!--cannot use due to .net runtime bug still being worked out to be fixed-->
        <!--<PackageReference Include="Markdig" Version="0.41.1" />-->
        <PackageReference Include="CommonMark.NET" Version="0.15.1" />

        <PackageReference Include="SkiaSharp.Views.Maui.Controls" Version="3.119.0" />
        <PackageReference Include="SkiaSharp.Skottie" Version="3.119.0" />
        <PackageReference Include="Svg.Skia" Version="3.0.3" />

        <!--<PackageReference Include="SkiaSharp.Views.Maui.Controls" Version="3.116.1" />
        <PackageReference Include="SkiaSharp.Skottie" Version="3.116.1" />
        <PackageReference Include="Svg.Skia" Version="2.0.0.4" />-->
      
        <!--todo move used code here-->
        <PackageReference Include="AppoMobi.Maui.Navigation" Version="1.9.3-pre" />

        <!--MIT open source-->
        <PackageReference Include="AppoMobi.Maui.Gestures" Version="1.9.7" />
        <!--MIT open source .net helpers and extensions-->
        <PackageReference Include="AppoMobi.Specials" Version="9.0.3" />
        <!--caching bitmaps etc-->
        <PackageReference Include="EasyCaching.InMemory" Version="1.9.2" />
    </ItemGroup>

    <ItemGroup Condition="$(TargetFramework.Contains('android')) != true">
        <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.6" />
    </ItemGroup>

    <!--ANDROID ONLY-->
    <ItemGroup Condition="$(TargetFramework.Contains('android')) == true">
        <!--fix android bad alignement-->
        <PackageReference Include="HarfBuzzSharp" Version="8.3.1.1" />
        <!--native interop todo expose source  todo check no more needed? -->
       <PackageReference Include="AppoMobi.Maui.Native" Version="1.0.1.0-pre" />
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Remove="Controls\Extensions\**" />
        <EmbeddedResource Remove="Platforms\Windows\Controls\**" />
    </ItemGroup>

    <ItemGroup>
        <MauiXaml Remove="Controls\Extensions\**" />
        <MauiXaml Remove="Platforms\Windows\Controls\**" />
    </ItemGroup>

    <ItemGroup>
        <MauiCss Remove="Controls\Extensions\**" />
        <MauiCss Remove="Platforms\Windows\Controls\**" />
    </ItemGroup>
    <ItemGroup>
        <AndroidAarLibrary Remove="Controls\Extensions\**" />
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Internals\Handlers\" />
    </ItemGroup>


    <ItemGroup>
        <Compile Remove="Shared/**/*.*pple.cs" />
        <None Include="Shared/**/*.*pple.cs" />
    </ItemGroup>

    <ItemGroup Condition="$(TargetFramework.Contains('catalyst'))">
        <Compile Include="Shared/**/*.*pple.cs">
            <Link>Platforms/MacCatalyst/%(RecursiveDir)%(Filename)%(Extension)</Link>
        </Compile>
    </ItemGroup>

    <ItemGroup Condition="$(TargetFramework.Contains('ios'))">
        <Compile Include="Shared/**/*.*pple.cs">
            <Link>Platforms/iOS/%(RecursiveDir)%(Filename)%(Extension)</Link>
        </Compile>
    </ItemGroup>

    <Import Project="..\..\Shared\Shared.projitems" Label="Shared" />


</Project>